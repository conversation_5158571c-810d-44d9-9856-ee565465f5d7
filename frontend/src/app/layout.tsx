import type { <PERSON>ada<PERSON> } from 'next';
import { Inter } from 'next/font/google';
import './globals.css';
import { TokenManager } from '@/components/auth/token-manager';

const inter = Inter({ subsets: ['latin'] });

export const metadata: Metadata = {
  title: 'CVE Feed Service',
  description: 'Comprehensive vulnerability management platform for tracking CVEs, managing applications, and monitoring security threats in real-time.',
  keywords: 'CVE, vulnerability, security, management, monitoring, cybersecurity',
  authors: [{ name: 'CVE Feed Service Team' }],
  viewport: 'width=device-width, initial-scale=1, maximum-scale=1, user-scalable=no',
  manifest: '/manifest.json',
  themeColor: '#06b6d4',
  appleWebApp: {
    capable: true,
    statusBarStyle: 'default',
    title: 'CVE Feed Service'
  },
  other: {
    'mobile-web-app-capable': 'yes',
    'apple-mobile-web-app-capable': 'yes',
    'apple-mobile-web-app-status-bar-style': 'default',
    'apple-mobile-web-app-title': 'CVE Feed Service',
    'application-name': 'CVE Feed Service',
    'msapplication-TileColor': '#06b6d4'
  }
};

export default function RootLayout({
  children,
}: {
  children: React.ReactNode;
}) {
  return (
    <html lang="en" className="dark">
      <body className={inter.className}>
        <TokenManager>
          {children}
        </TokenManager>
      </body>
    </html>
  );
}
