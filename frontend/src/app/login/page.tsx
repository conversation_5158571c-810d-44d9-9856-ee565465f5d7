'use client';

import { useState } from 'react';
import { useRouter } from 'next/navigation';
import { useForm } from 'react-hook-form';
import { zodResolver } from '@hookform/resolvers/zod';
import { z } from 'zod';
import { Eye, EyeOff, Shield, AlertCircle } from 'lucide-react';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Checkbox } from '@/components/ui/checkbox';
import { Alert, AlertDescription } from '@/components/ui/alert';
import { useAuth } from '@/stores/auth-store';
import { cn } from '@/lib/utils';

// Validation schema according to PRD specifications
const loginSchema = z.object({
  username: z
    .string()
    .min(1, 'Username is required')
    .email('Please enter a valid email address'),
  password: z
    .string()
    .min(8, 'Password must be at least 8 characters')
    .regex(/^(?=.*[a-z])(?=.*[A-Z])(?=.*\d)/, 'Password must contain uppercase, lowercase, and number'),
  rememberMe: z.boolean().optional(),
});

type LoginFormData = z.infer<typeof loginSchema>;

export default function LoginPage() {
  const router = useRouter();
  const { login, isLoading, error, clearError } = useAuth();
  const [showPassword, setShowPassword] = useState(false);

  const {
    register,
    handleSubmit,
    formState: { errors, isSubmitting },
    setValue,
    watch,
  } = useForm<LoginFormData>({
    resolver: zodResolver(loginSchema),
    defaultValues: {
      username: '',
      password: '',
      rememberMe: false,
    },
  });

  const onSubmit = async (data: LoginFormData) => {
    try {
      clearError();
      await login({
        username: data.username,
        password: data.password,
      });

      // Redirect to dashboard on successful login
      router.push('/dashboard');
    } catch (error) {
      // Error is handled by the auth store
      console.error('Login failed:', error);
    }
  };

  const togglePasswordVisibility = () => {
    setShowPassword(!showPassword);
  };

  return (
    <div className="min-h-screen bg-slate-900 flex items-center justify-center p-4">
      <div className="w-full max-w-md">
        {/* Header */}
        <div className="text-center mb-8">
          <div className="flex justify-center mb-4">
            <div className="p-3 bg-cyan-500/10 rounded-full">
              <Shield className="h-8 w-8 text-cyan-400" />
            </div>
          </div>
          <h1 className="text-2xl font-bold text-slate-50 mb-2">
            CVE Feed Service
          </h1>
          <p className="text-slate-400">
            Sign in to your vulnerability management dashboard
          </p>
        </div>

        {/* Login Form */}
        <div className="bg-slate-800 rounded-lg border border-slate-700 p-6 shadow-xl">
          <form onSubmit={handleSubmit(onSubmit)} className="space-y-6">
            {/* Error Alert */}
            {error && (
              <Alert variant="destructive" className="bg-red-950/50 border-red-800">
                <AlertCircle className="h-4 w-4" />
                <AlertDescription>{error}</AlertDescription>
              </Alert>
            )}

            {/* Username Field */}
            <div className="space-y-2">
              <Label htmlFor="username" className="text-slate-200">
                Email Address
              </Label>
              <Input
                id="username"
                type="email"
                placeholder="<EMAIL>"
                className={cn(
                  "bg-slate-700 border-slate-600 text-slate-50 placeholder:text-slate-400",
                  "focus:border-cyan-400 focus:ring-cyan-400",
                  errors.username && "border-red-500 focus:border-red-500 focus:ring-red-500"
                )}
                {...register('username')}
              />
              {errors.username && (
                <p className="text-sm text-red-400">{errors.username.message}</p>
              )}
            </div>

            {/* Password Field */}
            <div className="space-y-2">
              <Label htmlFor="password" className="text-slate-200">
                Password
              </Label>
              <div className="relative">
                <Input
                  id="password"
                  type={showPassword ? 'text' : 'password'}
                  placeholder="Enter your password"
                  className={cn(
                    "bg-slate-700 border-slate-600 text-slate-50 placeholder:text-slate-400 pr-10",
                    "focus:border-cyan-400 focus:ring-cyan-400",
                    errors.password && "border-red-500 focus:border-red-500 focus:ring-red-500"
                  )}
                  {...register('password')}
                />
                <button
                  type="button"
                  onClick={togglePasswordVisibility}
                  className="absolute right-3 top-1/2 -translate-y-1/2 text-slate-400 hover:text-slate-300"
                >
                  {showPassword ? (
                    <EyeOff className="h-4 w-4" />
                  ) : (
                    <Eye className="h-4 w-4" />
                  )}
                </button>
              </div>
              {errors.password && (
                <p className="text-sm text-red-400">{errors.password.message}</p>
              )}
            </div>

            {/* Remember Me */}
            <div className="flex items-center space-x-2">
              <Checkbox
                id="rememberMe"
                checked={watch('rememberMe')}
                onCheckedChange={(checked) => setValue('rememberMe', !!checked)}
                className="border-slate-600 data-[state=checked]:bg-cyan-500 data-[state=checked]:border-cyan-500"
              />
              <Label htmlFor="rememberMe" className="text-slate-300 text-sm">
                Remember me for 30 days
              </Label>
            </div>

            {/* Submit Button */}
            <Button
              type="submit"
              disabled={isLoading || isSubmitting}
              className="w-full bg-cyan-500 hover:bg-cyan-600 text-white font-medium"
            >
              {isLoading || isSubmitting ? (
                <div className="flex items-center space-x-2">
                  <div className="w-4 h-4 border-2 border-white/30 border-t-white rounded-full animate-spin" />
                  <span>Signing in...</span>
                </div>
              ) : (
                'Sign In'
              )}
            </Button>
          </form>

          {/* Footer Links */}
          <div className="mt-6 text-center space-y-2">
            <p className="text-slate-400 text-sm">
              Don't have an account?{' '}
              <button
                onClick={() => router.push('/register')}
                className="text-cyan-400 hover:text-cyan-300 font-medium"
              >
                Request Access
              </button>
            </p>
            <p className="text-slate-500 text-xs">
              Forgot your password?{' '}
              <button
                onClick={() => router.push('/forgot-password')}
                className="text-cyan-400 hover:text-cyan-300"
              >
                Reset it here
              </button>
            </p>
          </div>
        </div>

        {/* Demo Credentials */}
        <div className="mt-6 p-4 bg-slate-800/50 rounded-lg border border-slate-700">
          <h3 className="text-slate-300 text-sm font-medium mb-2">Demo Credentials</h3>
          <div className="text-slate-400 text-xs space-y-1">
            <p><strong>Email:</strong> <EMAIL></p>
            <p><strong>Password:</strong> password123</p>
            <p><strong>Role:</strong> Security Analyst</p>
          </div>
        </div>
      </div>
    </div>
  );
}
