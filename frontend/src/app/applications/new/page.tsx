'use client';

import { useState } from 'react';
import { useRouter } from 'next/navigation';
import { ApplicationForm } from '@/components/forms/application-form';
import { ApplicationCreateRequest } from '@/types';

export default function NewApplicationPage() {
  const router = useRouter();
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);

  const handleSubmit = async (data: ApplicationCreateRequest) => {
    setIsLoading(true);
    setError(null);

    try {
      // Simulate API call
      console.log('Creating application:', data);
      
      const response = await fetch('/api/v1/applications', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(data),
      });

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.detail || 'Failed to create application');
      }

      const result = await response.json();
      console.log('Application created:', result);

      // Redirect to applications list
      router.push('/applications?message=created');
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : 'Failed to create application';
      setError(errorMessage);
      console.error('Application creation failed:', error);
    } finally {
      setIsLoading(false);
    }
  };

  const handleCancel = () => {
    router.push('/applications');
  };

  return (
    <div className="container mx-auto py-6 space-y-6">
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-3xl font-bold text-slate-50">Add New Application</h1>
          <p className="text-slate-400 mt-2">
            Register a new application for vulnerability monitoring and management.
          </p>
        </div>
      </div>

      <ApplicationForm
        onSubmit={handleSubmit}
        onCancel={handleCancel}
        loading={isLoading}
        error={error}
        mode="create"
      />
    </div>
  );
}
