/**
 * CVE Management UX Component Tests
 * Tests for user experience and component interactions in CVE management
 */

import { describe, it, expect, beforeEach, jest } from '@jest/globals';
import { render, screen, fireEvent, waitFor, within } from '@testing-library/react';
import userEvent from '@testing-library/user-event';
import { QueryClient, QueryClientProvider } from '@tanstack/react-query';

// Mock components for testing
const MockProviders = ({ children }: { children: React.ReactNode }) => {
  const queryClient = new QueryClient({
    defaultOptions: {
      queries: { retry: false },
      mutations: { retry: false },
    },
  });

  return (
    <QueryClientProvider client={queryClient}>
      {children}
    </QueryClientProvider>
  );
};

// Mock CVE data
const mockCVEs = [
  {
    id: 'CVE-2024-0001',
    description: 'Critical remote code execution vulnerability in web framework',
    severity: 'CRITICAL',
    cvss_score: 9.8,
    published_date: '2024-01-20T10:00:00Z',
    status: 'published',
    affected_applications: 5,
    patch_available: true,
    exploit_available: true,
    trending: true,
    tags: ['rce', 'critical', 'web']
  },
  {
    id: 'CVE-2024-0002',
    description: 'High severity privilege escalation in authentication module',
    severity: 'HIGH',
    cvss_score: 8.5,
    published_date: '2024-01-19T14:00:00Z',
    status: 'published',
    affected_applications: 3,
    patch_available: false,
    exploit_available: false,
    trending: false,
    tags: ['privilege-escalation', 'auth']
  }
];

const mockCVEDetails = {
  ...mockCVEs[0],
  technical_details: {
    attack_vector: 'Network',
    attack_complexity: 'Low',
    privileges_required: 'None',
    user_interaction: 'None',
    scope: 'Unchanged',
    confidentiality_impact: 'High',
    integrity_impact: 'High',
    availability_impact: 'High'
  },
  affected_versions: [
    {
      product: 'WebFramework',
      vendor: 'Example Corp',
      versions: ['2.0', '2.1', '2.2'],
      fixed_versions: ['2.3']
    }
  ],
  remediation: {
    patches: [
      {
        version: '2.3',
        release_date: '2024-01-22T00:00:00Z',
        download_url: 'https://example.com/patches/v2.3'
      }
    ],
    workarounds: ['Disable affected module until patch is applied'],
    mitigations: ['Deploy Web Application Firewall rules']
  }
};

// Mock fetch
global.fetch = jest.fn(() =>
  Promise.resolve({
    ok: true,
    json: () => Promise.resolve({
      cves: mockCVEs,
      total: mockCVEs.length
    }),
  })
) as any;

// Mock CVE components (these would be actual components in the real app)
const CVEListPage = () => (
  <div data-testid="cve-list-page">
    <h1>CVE Management</h1>
    <div data-testid="cve-filters">
      <select data-testid="severity-filter">
        <option value="">All Severities</option>
        <option value="critical">Critical</option>
        <option value="high">High</option>
        <option value="medium">Medium</option>
        <option value="low">Low</option>
      </select>
      <input data-testid="search-input" placeholder="Search CVEs..." />
      <button data-testid="filter-trending">Trending Only</button>
    </div>
    <div data-testid="cve-list">
      {mockCVEs.map(cve => (
        <div key={cve.id} data-testid={`cve-item-${cve.id}`} className="cve-item">
          <div data-testid="cve-id">{cve.id}</div>
          <div data-testid="cve-severity" className={`severity-${cve.severity.toLowerCase()}`}>
            {cve.severity}
          </div>
          <div data-testid="cve-score">{cve.cvss_score}</div>
          <div data-testid="cve-description">{cve.description}</div>
          {cve.trending && <span data-testid="trending-badge">Trending</span>}
          {cve.patch_available && <span data-testid="patch-badge">Patch Available</span>}
          {cve.exploit_available && <span data-testid="exploit-badge">Exploit Available</span>}
        </div>
      ))}
    </div>
  </div>
);

const CVEDetailsPage = ({ cveId }: { cveId: string }) => (
  <div data-testid="cve-details-page">
    <h1 data-testid="cve-title">{cveId}</h1>
    <div data-testid="cve-overview">
      <div data-testid="severity-indicator" className={`severity-${mockCVEDetails.severity.toLowerCase()}`}>
        {mockCVEDetails.severity}
      </div>
      <div data-testid="cvss-score">{mockCVEDetails.cvss_score}</div>
      <div data-testid="cve-description">{mockCVEDetails.description}</div>
    </div>
    
    <div data-testid="technical-details">
      <h2>Technical Details</h2>
      <div data-testid="attack-vector">{mockCVEDetails.technical_details.attack_vector}</div>
      <div data-testid="attack-complexity">{mockCVEDetails.technical_details.attack_complexity}</div>
    </div>
    
    <div data-testid="affected-products">
      <h2>Affected Products</h2>
      {mockCVEDetails.affected_versions.map((version, index) => (
        <div key={index} data-testid={`affected-product-${index}`}>
          <span data-testid="product-name">{version.product}</span>
          <span data-testid="vendor-name">{version.vendor}</span>
        </div>
      ))}
    </div>
    
    <div data-testid="remediation-section">
      <h2>Remediation</h2>
      <div data-testid="patches">
        {mockCVEDetails.remediation.patches.map((patch, index) => (
          <div key={index} data-testid={`patch-${index}`}>
            <span data-testid="patch-version">{patch.version}</span>
            <a data-testid="patch-download" href={patch.download_url}>Download</a>
          </div>
        ))}
      </div>
    </div>
    
    <div data-testid="action-buttons">
      <button data-testid="add-to-watchlist">Add to Watchlist</button>
      <button data-testid="export-cve">Export</button>
      <button data-testid="share-cve">Share</button>
    </div>
  </div>
);

describe('CVE Management UX Component Tests', () => {
  let user: ReturnType<typeof userEvent.setup>;

  beforeEach(() => {
    user = userEvent.setup();
    jest.clearAllMocks();
  });

  describe('CVE List Page', () => {
    it('should render CVE list with proper structure', () => {
      render(
        <MockProviders>
          <CVEListPage />
        </MockProviders>
      );

      expect(screen.getByRole('heading', { name: /cve management/i })).toBeInTheDocument();
      expect(screen.getByTestId('cve-filters')).toBeInTheDocument();
      expect(screen.getByTestId('cve-list')).toBeInTheDocument();
    });

    it('should display CVE items with essential information', () => {
      render(
        <MockProviders>
          <CVEListPage />
        </MockProviders>
      );

      expect(screen.getByTestId('cve-item-CVE-2024-0001')).toBeInTheDocument();
      expect(screen.getByText('CVE-2024-0001')).toBeInTheDocument();
      expect(screen.getByText('CRITICAL')).toBeInTheDocument();
      expect(screen.getByText('9.8')).toBeInTheDocument();
    });

    it('should show severity with appropriate styling', () => {
      render(
        <MockProviders>
          <CVEListPage />
        </MockProviders>
      );

      const criticalSeverity = screen.getByText('CRITICAL');
      expect(criticalSeverity).toHaveClass('severity-critical');

      const highSeverity = screen.getByText('HIGH');
      expect(highSeverity).toHaveClass('severity-high');
    });

    it('should display status badges correctly', () => {
      render(
        <MockProviders>
          <CVEListPage />
        </MockProviders>
      );

      expect(screen.getByTestId('trending-badge')).toBeInTheDocument();
      expect(screen.getByTestId('patch-badge')).toBeInTheDocument();
      expect(screen.getByTestId('exploit-badge')).toBeInTheDocument();
    });

    it('should handle severity filter interaction', async () => {
      render(
        <MockProviders>
          <CVEListPage />
        </MockProviders>
      );

      const severityFilter = screen.getByTestId('severity-filter');
      await user.selectOptions(severityFilter, 'critical');

      expect(severityFilter).toHaveValue('critical');
    });

    it('should handle search input', async () => {
      render(
        <MockProviders>
          <CVEListPage />
        </MockProviders>
      );

      const searchInput = screen.getByTestId('search-input');
      await user.type(searchInput, 'remote code execution');

      expect(searchInput).toHaveValue('remote code execution');
    });

    it('should handle trending filter toggle', async () => {
      render(
        <MockProviders>
          <CVEListPage />
        </MockProviders>
      );

      const trendingButton = screen.getByTestId('filter-trending');
      await user.click(trendingButton);

      // Button should show active state
      expect(trendingButton).toHaveClass(/active|selected/);
    });

    it('should support keyboard navigation', async () => {
      render(
        <MockProviders>
          <CVEListPage />
        </MockProviders>
      );

      const firstCVE = screen.getByTestId('cve-item-CVE-2024-0001');
      
      // Tab to first CVE item
      await user.tab();
      expect(firstCVE).toHaveFocus();

      // Enter should navigate to details
      await user.keyboard('{Enter}');
      // Would trigger navigation in real app
    });
  });

  describe('CVE Details Page', () => {
    it('should render CVE details with comprehensive information', () => {
      render(
        <MockProviders>
          <CVEDetailsPage cveId="CVE-2024-0001" />
        </MockProviders>
      );

      expect(screen.getByTestId('cve-title')).toHaveTextContent('CVE-2024-0001');
      expect(screen.getByTestId('cve-overview')).toBeInTheDocument();
      expect(screen.getByTestId('technical-details')).toBeInTheDocument();
      expect(screen.getByTestId('affected-products')).toBeInTheDocument();
      expect(screen.getByTestId('remediation-section')).toBeInTheDocument();
    });

    it('should display severity indicator with proper styling', () => {
      render(
        <MockProviders>
          <CVEDetailsPage cveId="CVE-2024-0001" />
        </MockProviders>
      );

      const severityIndicator = screen.getByTestId('severity-indicator');
      expect(severityIndicator).toHaveTextContent('CRITICAL');
      expect(severityIndicator).toHaveClass('severity-critical');
    });

    it('should show technical details clearly', () => {
      render(
        <MockProviders>
          <CVEDetailsPage cveId="CVE-2024-0001" />
        </MockProviders>
      );

      expect(screen.getByTestId('attack-vector')).toHaveTextContent('Network');
      expect(screen.getByTestId('attack-complexity')).toHaveTextContent('Low');
    });

    it('should display affected products information', () => {
      render(
        <MockProviders>
          <CVEDetailsPage cveId="CVE-2024-0001" />
        </MockProviders>
      );

      expect(screen.getByTestId('product-name')).toHaveTextContent('WebFramework');
      expect(screen.getByTestId('vendor-name')).toHaveTextContent('Example Corp');
    });

    it('should show remediation options', () => {
      render(
        <MockProviders>
          <CVEDetailsPage cveId="CVE-2024-0001" />
        </MockProviders>
      );

      expect(screen.getByTestId('patch-version')).toHaveTextContent('2.3');
      expect(screen.getByTestId('patch-download')).toHaveAttribute('href', 'https://example.com/patches/v2.3');
    });

    it('should handle action button interactions', async () => {
      render(
        <MockProviders>
          <CVEDetailsPage cveId="CVE-2024-0001" />
        </MockProviders>
      );

      const watchlistButton = screen.getByTestId('add-to-watchlist');
      await user.click(watchlistButton);

      // Button should change state or show confirmation
      expect(watchlistButton).toBeInTheDocument();
    });

    it('should support export functionality', async () => {
      render(
        <MockProviders>
          <CVEDetailsPage cveId="CVE-2024-0001" />
        </MockProviders>
      );

      const exportButton = screen.getByTestId('export-cve');
      await user.click(exportButton);

      // Should trigger export functionality
      expect(exportButton).toBeInTheDocument();
    });
  });

  describe('Accessibility Features', () => {
    it('should have proper heading hierarchy', () => {
      render(
        <MockProviders>
          <CVEDetailsPage cveId="CVE-2024-0001" />
        </MockProviders>
      );

      const headings = screen.getAllByRole('heading');
      expect(headings[0]).toHaveAttribute('data-testid', 'cve-title');
      expect(headings.length).toBeGreaterThan(1);
    });

    it('should provide ARIA labels for interactive elements', () => {
      render(
        <MockProviders>
          <CVEListPage />
        </MockProviders>
      );

      const searchInput = screen.getByTestId('search-input');
      expect(searchInput).toHaveAttribute('placeholder', 'Search CVEs...');
    });

    it('should support screen reader navigation', () => {
      render(
        <MockProviders>
          <CVEDetailsPage cveId="CVE-2024-0001" />
        </MockProviders>
      );

      // Check for semantic structure
      expect(screen.getByRole('heading', { level: 1 })).toBeInTheDocument();
      expect(screen.getAllByRole('heading', { level: 2 })).toHaveLength(3);
    });

    it('should have sufficient color contrast for severity indicators', () => {
      render(
        <MockProviders>
          <CVEListPage />
        </MockProviders>
      );

      const criticalSeverity = screen.getByText('CRITICAL');
      const highSeverity = screen.getByText('HIGH');

      // These should have appropriate CSS classes for contrast
      expect(criticalSeverity).toHaveClass('severity-critical');
      expect(highSeverity).toHaveClass('severity-high');
    });
  });

  describe('Responsive Design', () => {
    it('should adapt layout for mobile screens', () => {
      // Mock mobile viewport
      Object.defineProperty(window, 'innerWidth', {
        writable: true,
        configurable: true,
        value: 375,
      });

      render(
        <MockProviders>
          <CVEListPage />
        </MockProviders>
      );

      const cveList = screen.getByTestId('cve-list');
      expect(cveList).toBeInTheDocument();
      // Should have mobile-responsive classes
    });

    it('should handle touch interactions on mobile', async () => {
      render(
        <MockProviders>
          <CVEListPage />
        </MockProviders>
      );

      const cveItem = screen.getByTestId('cve-item-CVE-2024-0001');
      
      // Simulate touch interaction
      fireEvent.touchStart(cveItem);
      fireEvent.touchEnd(cveItem);

      expect(cveItem).toBeInTheDocument();
    });
  });

  describe('Error Handling', () => {
    it('should show error state when CVE data fails to load', async () => {
      global.fetch = jest.fn(() =>
        Promise.reject(new Error('Network error'))
      ) as any;

      render(
        <MockProviders>
          <CVEListPage />
        </MockProviders>
      );

      // Should show error message
      await waitFor(() => {
        expect(screen.queryByText(/error loading/i)).toBeInTheDocument();
      });
    });

    it('should handle empty CVE list gracefully', () => {
      global.fetch = jest.fn(() =>
        Promise.resolve({
          ok: true,
          json: () => Promise.resolve({ cves: [], total: 0 }),
        })
      ) as any;

      render(
        <MockProviders>
          <CVEListPage />
        </MockProviders>
      );

      // Should show empty state message
      expect(screen.queryByText(/no cves found/i)).toBeInTheDocument();
    });
  });

  describe('Performance and Loading States', () => {
    it('should show loading skeletons while data loads', () => {
      render(
        <MockProviders>
          <CVEListPage />
        </MockProviders>
      );

      // Should show loading indicators initially
      expect(screen.queryByTestId('cve-list-skeleton')).toBeInTheDocument();
    });

    it('should handle large CVE lists efficiently', () => {
      // Mock large dataset
      const largeCVEList = Array(1000).fill(null).map((_, index) => ({
        ...mockCVEs[0],
        id: `CVE-2024-${String(index).padStart(4, '0')}`
      }));

      global.fetch = vi.fn(() =>
        Promise.resolve({
          ok: true,
          json: () => Promise.resolve({ cves: largeCVEList.slice(0, 50), total: largeCVEList.length }),
        })
      ) as any;

      render(
        <MockProviders>
          <CVEListPage />
        </MockProviders>
      );

      // Should handle pagination or virtualization
      expect(screen.getByTestId('cve-list')).toBeInTheDocument();
    });
  });

  describe('Real-time Updates', () => {
    it('should handle real-time CVE updates', async () => {
      render(
        <MockProviders>
          <CVEListPage />
        </MockProviders>
      );

      // Simulate real-time update
      const updateEvent = new CustomEvent('cve-update', {
        detail: { cveId: 'CVE-2024-0001', type: 'severity_change' }
      });

      fireEvent(window, updateEvent);

      // Should show update indicator
      await waitFor(() => {
        expect(screen.queryByTestId('update-indicator')).toBeInTheDocument();
      });
    });
  });
});
