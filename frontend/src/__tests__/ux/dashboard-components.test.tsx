/**
 * Dashboard UX Component Tests
 * Tests for user experience and component interactions
 */

import { describe, it, expect, beforeEach, jest } from '@jest/globals';
import { render, screen, fireEvent, waitFor, within } from '@testing-library/react';
import userEvent from '@testing-library/user-event';
import { QueryClient, QueryClientProvider } from '@tanstack/react-query';
import DashboardPage from '@/app/dashboard/page';

// Mock components for testing
const MockProviders = ({ children }: { children: React.ReactNode }) => {
  const queryClient = new QueryClient({
    defaultOptions: {
      queries: { retry: false },
      mutations: { retry: false },
    },
  });

  return (
    <QueryClientProvider client={queryClient}>
      {children}
    </QueryClientProvider>
  );
};

// Mock data
const mockDashboardData = {
  metrics: {
    total_cves: 1247,
    critical_cves: 23,
    high_cves: 156,
    medium_cves: 489,
    low_cves: 579,
    total_applications: 45,
    vulnerable_applications: 12,
    compliance_score: 87.5,
    last_updated: '2024-01-22T10:30:00Z'
  },
  trends: [
    { date: '2024-01-15', critical: 20, high: 145, medium: 478, low: 567 },
    { date: '2024-01-16', critical: 21, high: 148, medium: 482, low: 571 },
    { date: '2024-01-17', critical: 22, high: 152, medium: 485, low: 575 }
  ],
  topVulnerabilities: [
    {
      id: 'CVE-2024-0001',
      severity: 'CRITICAL',
      score: 9.8,
      affected_apps: 3,
      description: 'Critical remote code execution vulnerability'
    }
  ],
  recentActivities: [
    {
      id: '1',
      type: 'cve_discovered',
      description: 'New critical CVE discovered',
      timestamp: '2024-01-22T09:15:00Z',
      user: 'system'
    }
  ]
};

// Mock fetch
global.fetch = jest.fn(() =>
  Promise.resolve({
    ok: true,
    json: () => Promise.resolve(mockDashboardData),
  })
) as any;

describe('Dashboard UX Component Tests', () => {
  let user: ReturnType<typeof userEvent.setup>;

  beforeEach(() => {
    user = userEvent.setup();
    jest.clearAllMocks();
  });

  describe('Dashboard Layout and Structure', () => {
    it('should render dashboard with proper heading structure', async () => {
      render(
        <MockProviders>
          <DashboardPage />
        </MockProviders>
      );

      expect(screen.getByRole('heading', { level: 1 })).toBeInTheDocument();
      expect(screen.getByText(/security dashboard/i)).toBeInTheDocument();
    });

    it('should display metric cards in a grid layout', async () => {
      render(
        <MockProviders>
          <DashboardPage />
        </MockProviders>
      );

      await waitFor(() => {
        const metricCards = screen.getAllByTestId(/metric-card/);
        expect(metricCards.length).toBeGreaterThan(0);
      });
    });

    it('should have proper semantic structure for screen readers', async () => {
      render(
        <MockProviders>
          <DashboardPage />
        </MockProviders>
      );

      // Check for proper landmarks
      expect(screen.getByRole('main')).toBeInTheDocument();
      
      // Check for proper headings hierarchy
      const headings = screen.getAllByRole('heading');
      expect(headings.length).toBeGreaterThan(1);
    });
  });

  describe('Metric Cards Interaction', () => {
    it('should display metric values with proper formatting', async () => {
      render(
        <MockProviders>
          <DashboardPage />
        </MockProviders>
      );

      await waitFor(() => {
        expect(screen.getByText('1,247')).toBeInTheDocument(); // Total CVEs
        expect(screen.getByText('23')).toBeInTheDocument(); // Critical CVEs
        expect(screen.getByText('87.5%')).toBeInTheDocument(); // Compliance score
      });
    });

    it('should show loading states for metric cards', () => {
      render(
        <MockProviders>
          <DashboardPage />
        </MockProviders>
      );

      // Should show loading skeletons initially
      const loadingElements = screen.getAllByTestId(/loading|skeleton/);
      expect(loadingElements.length).toBeGreaterThan(0);
    });

    it('should handle metric card hover interactions', async () => {
      render(
        <MockProviders>
          <DashboardPage />
        </MockProviders>
      );

      await waitFor(() => {
        const metricCard = screen.getByTestId('critical-cves-card');
        expect(metricCard).toBeInTheDocument();
      });

      const metricCard = screen.getByTestId('critical-cves-card');
      await user.hover(metricCard);

      // Should show additional information on hover
      expect(metricCard).toHaveClass(/hover/);
    });

    it('should navigate to detailed view when metric card is clicked', async () => {
      const mockNavigate = jest.fn();
      jest.mock('next/navigation', () => ({
        useRouter: () => ({ push: mockNavigate }),
      }));

      render(
        <MockProviders>
          <DashboardPage />
        </MockProviders>
      );

      await waitFor(() => {
        const criticalCvesCard = screen.getByTestId('critical-cves-card');
        expect(criticalCvesCard).toBeInTheDocument();
      });

      const criticalCvesCard = screen.getByTestId('critical-cves-card');
      await user.click(criticalCvesCard);

      expect(mockNavigate).toHaveBeenCalledWith('/cves?severity=critical');
    });
  });

  describe('Trend Visualization', () => {
    it('should render trend chart with proper accessibility', async () => {
      render(
        <MockProviders>
          <DashboardPage />
        </MockProviders>
      );

      await waitFor(() => {
        const trendChart = screen.getByTestId('vulnerability-trends-chart');
        expect(trendChart).toBeInTheDocument();
      });

      const trendChart = screen.getByTestId('vulnerability-trends-chart');
      expect(trendChart).toHaveAttribute('role', 'img');
      expect(trendChart).toHaveAttribute('aria-label');
    });

    it('should show trend indicators with proper colors', async () => {
      render(
        <MockProviders>
          <DashboardPage />
        </MockProviders>
      );

      await waitFor(() => {
        const trendUp = screen.getByTestId('trend-up-indicator');
        const trendDown = screen.getByTestId('trend-down-indicator');
        
        expect(trendUp).toHaveClass(/text-green/);
        expect(trendDown).toHaveClass(/text-red/);
      });
    });

    it('should allow time period selection for trends', async () => {
      render(
        <MockProviders>
          <DashboardPage />
        </MockProviders>
      );

      await waitFor(() => {
        const timePeriodSelect = screen.getByTestId('time-period-select');
        expect(timePeriodSelect).toBeInTheDocument();
      });

      const timePeriodSelect = screen.getByTestId('time-period-select');
      await user.click(timePeriodSelect);

      const option30Days = screen.getByText('Last 30 days');
      await user.click(option30Days);

      // Should trigger data refresh
      expect(fetch).toHaveBeenCalledWith(
        expect.stringContaining('trends?days=30')
      );
    });
  });

  describe('Top Vulnerabilities Section', () => {
    it('should display vulnerability list with proper information', async () => {
      render(
        <MockProviders>
          <DashboardPage />
        </MockProviders>
      );

      await waitFor(() => {
        expect(screen.getByText('CVE-2024-0001')).toBeInTheDocument();
        expect(screen.getByText('9.8')).toBeInTheDocument();
        expect(screen.getByText('3 apps affected')).toBeInTheDocument();
      });
    });

    it('should show severity badges with appropriate colors', async () => {
      render(
        <MockProviders>
          <DashboardPage />
        </MockProviders>
      );

      await waitFor(() => {
        const criticalBadge = screen.getByText('CRITICAL');
        expect(criticalBadge).toHaveClass(/bg-red/);
      });
    });

    it('should allow navigation to vulnerability details', async () => {
      const mockNavigate = jest.fn();
      jest.mock('next/navigation', () => ({
        useRouter: () => ({ push: mockNavigate }),
      }));

      render(
        <MockProviders>
          <DashboardPage />
        </MockProviders>
      );

      await waitFor(() => {
        const vulnLink = screen.getByText('CVE-2024-0001');
        expect(vulnLink).toBeInTheDocument();
      });

      const vulnLink = screen.getByText('CVE-2024-0001');
      await user.click(vulnLink);

      expect(mockNavigate).toHaveBeenCalledWith('/cves/CVE-2024-0001');
    });
  });

  describe('Recent Activities Section', () => {
    it('should display activities with timestamps', async () => {
      render(
        <MockProviders>
          <DashboardPage />
        </MockProviders>
      );

      await waitFor(() => {
        expect(screen.getByText('New critical CVE discovered')).toBeInTheDocument();
        expect(screen.getByText(/2024-01-22/)).toBeInTheDocument();
      });
    });

    it('should show activity type icons', async () => {
      render(
        <MockProviders>
          <DashboardPage />
        </MockProviders>
      );

      await waitFor(() => {
        const activityIcon = screen.getByTestId('activity-icon-cve_discovered');
        expect(activityIcon).toBeInTheDocument();
      });
    });

    it('should format timestamps in user-friendly format', async () => {
      render(
        <MockProviders>
          <DashboardPage />
        </MockProviders>
      );

      await waitFor(() => {
        // Should show relative time like "2 hours ago"
        const timeElement = screen.getByTestId('activity-timestamp');
        expect(timeElement).toHaveTextContent(/ago|minutes|hours|days/);
      });
    });
  });

  describe('Responsive Design', () => {
    it('should adapt layout for mobile screens', () => {
      // Mock mobile viewport
      Object.defineProperty(window, 'innerWidth', {
        writable: true,
        configurable: true,
        value: 375,
      });

      render(
        <MockProviders>
          <DashboardPage />
        </MockProviders>
      );

      const container = screen.getByTestId('dashboard-container');
      expect(container).toHaveClass(/grid-cols-1|sm:grid-cols-2/);
    });

    it('should show/hide elements based on screen size', () => {
      render(
        <MockProviders>
          <DashboardPage />
        </MockProviders>
      );

      const mobileOnlyElement = screen.queryByTestId('mobile-only');
      const desktopOnlyElement = screen.queryByTestId('desktop-only');

      // These should have appropriate responsive classes
      if (mobileOnlyElement) {
        expect(mobileOnlyElement).toHaveClass(/block.*md:hidden/);
      }
      if (desktopOnlyElement) {
        expect(desktopOnlyElement).toHaveClass(/hidden.*md:block/);
      }
    });
  });

  describe('Accessibility Features', () => {
    it('should support keyboard navigation', async () => {
      render(
        <MockProviders>
          <DashboardPage />
        </MockProviders>
      );

      // Tab through interactive elements
      await user.tab();
      expect(document.activeElement).toHaveAttribute('tabindex', '0');

      await user.tab();
      expect(document.activeElement).toBeInstanceOf(HTMLElement);
    });

    it('should provide proper ARIA labels', async () => {
      render(
        <MockProviders>
          <DashboardPage />
        </MockProviders>
      );

      await waitFor(() => {
        const metricCard = screen.getByTestId('critical-cves-card');
        expect(metricCard).toHaveAttribute('aria-label');
      });
    });

    it('should announce dynamic content changes', async () => {
      render(
        <MockProviders>
          <DashboardPage />
        </MockProviders>
      );

      // Should have aria-live regions for dynamic updates
      const liveRegion = screen.getByTestId('metrics-live-region');
      expect(liveRegion).toHaveAttribute('aria-live', 'polite');
    });
  });

  describe('Error Handling UX', () => {
    it('should show error state when data fails to load', async () => {
      global.fetch = jest.fn(() =>
        Promise.reject(new Error('Network error'))
      ) as any;

      render(
        <MockProviders>
          <DashboardPage />
        </MockProviders>
      );

      await waitFor(() => {
        expect(screen.getByText(/error loading dashboard/i)).toBeInTheDocument();
        expect(screen.getByText(/try again/i)).toBeInTheDocument();
      });
    });

    it('should allow retry when error occurs', async () => {
      global.fetch = vi.fn(() =>
        Promise.reject(new Error('Network error'))
      ) as any;

      render(
        <MockProviders>
          <DashboardPage />
        </MockProviders>
      );

      await waitFor(() => {
        const retryButton = screen.getByText(/try again/i);
        expect(retryButton).toBeInTheDocument();
      });

      const retryButton = screen.getByText(/try again/i);
      await user.click(retryButton);

      expect(fetch).toHaveBeenCalledTimes(2);
    });
  });

  describe('Performance and Loading States', () => {
    it('should show skeleton loading for each section', () => {
      render(
        <MockProviders>
          <DashboardPage />
        </MockProviders>
      );

      expect(screen.getByTestId('metrics-skeleton')).toBeInTheDocument();
      expect(screen.getByTestId('trends-skeleton')).toBeInTheDocument();
      expect(screen.getByTestId('vulnerabilities-skeleton')).toBeInTheDocument();
    });

    it('should progressively load content sections', async () => {
      render(
        <MockProviders>
          <DashboardPage />
        </MockProviders>
      );

      // Metrics should load first
      await waitFor(() => {
        expect(screen.queryByTestId('metrics-skeleton')).not.toBeInTheDocument();
      });

      // Then other sections
      await waitFor(() => {
        expect(screen.queryByTestId('trends-skeleton')).not.toBeInTheDocument();
      });
    });
  });
});
